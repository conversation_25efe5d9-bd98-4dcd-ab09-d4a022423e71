import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

import '../../models/channel_model.dart';
import '../core/gf_card.dart';
import '../core/gf_button.dart';

/// GameFlex channel card component
///
/// A reusable channel card component that displays channel information
/// with consistent styling and interaction patterns.
///
/// Example usage:
/// ```dart
/// GFChannelCard(
///   channel: channel,
///   onTap: () => _navigateToChannel(channel),
///   onJoin: () => _joinChannel(channel),
///   onLeave: () => _leaveChannel(channel),
/// )
/// ```
class GFChannelCard extends StatelessWidget {
  final ChannelModel channel;
  final VoidCallback? onTap;
  final VoidCallback? onJoin;
  final VoidCallback? onLeave;
  final VoidCallback? onMore;
  final bool showJoinButton;
  final bool isJoined;
  final EdgeInsetsGeometry? margin;

  const GFChannelCard({
    super.key,
    required this.channel,
    this.onTap,
    this.onJoin,
    this.onLeave,
    this.onMore,
    this.showJoinButton = true,
    this.isJoined = false,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: margin,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: _getChannelGradient(),
        ),
        child: Stack(
          children: [
            // Background pattern/texture
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: Colors.black.withValues(alpha: 26), // 0.1 opacity
              ),
            ),

            // Content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Channel icon
                  _buildChannelIcon(),

                  const Spacer(),

                  // Channel info at bottom
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        channel.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${channel.memberCount} members',
                        style: TextStyle(
                          color: Colors.white.withValues(
                            alpha: 204,
                          ), // 0.8 opacity
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Join button overlay (only show if not joined and showJoinButton is true)
            if (showJoinButton && !isJoined)
              Positioned(top: 12, right: 12, child: _buildJoinButton()),
          ],
        ),
      ),
    );
  }

  /// Get gradient colors based on channel name
  LinearGradient _getChannelGradient() {
    // Create different gradients based on channel name hash
    final hash = channel.name.hashCode;
    final gradients = [
      // Minecraft-style (green/brown)
      const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
      ),
      // Fortnite-style (blue/purple)
      const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFF2196F3), Color(0xFF3F51B5)],
      ),
      // Valorant-style (red/orange)
      const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFFFF5722), Color(0xFFE91E63)],
      ),
      // Apex-style (orange/yellow)
      const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFFFF9800), Color(0xFFFFC107)],
      ),
      // COD-style (dark/gray)
      const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFF424242), Color(0xFF616161)],
      ),
      // General-style (teal/cyan)
      const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [Color(0xFF009688), Color(0xFF00BCD4)],
      ),
    ];

    return gradients[hash.abs() % gradients.length];
  }

  /// Build channel icon
  Widget _buildChannelIcon() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 51), // 0.2 opacity
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(_getChannelIcon(), color: Colors.white, size: 24),
    );
  }

  /// Get icon based on channel name
  IconData _getChannelIcon() {
    final name = channel.name.toLowerCase();
    if (name.contains('minecraft')) return Icons.view_in_ar;
    if (name.contains('fortnite')) return Icons.sports_esports;
    if (name.contains('valorant')) return Icons.gps_fixed;
    if (name.contains('apex')) return Icons.terrain;
    if (name.contains('cod') || name.contains('call of duty')) {
      return Icons.military_tech;
    }
    if (name.contains('general') || name.contains('gaming')) {
      return Icons.videogame_asset;
    }
    return Icons.tag; // Default icon
  }

  /// Build join button
  Widget _buildJoinButton() {
    return GestureDetector(
      onTap: onJoin,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Text(
          'Join',
          style: TextStyle(
            color: Colors.black,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }
}

/// Compact channel card for lists
class GFCompactChannelCard extends StatelessWidget {
  final ChannelModel channel;
  final VoidCallback? onTap;
  final bool isSelected;
  final Widget? trailing;

  const GFCompactChannelCard({
    super.key,
    required this.channel,
    this.onTap,
    this.isSelected = false,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return GFCard(
      onTap: onTap,
      padding: const EdgeInsets.all(12),
      backgroundColor:
          isSelected
              ? AppColors.gfGreen.withValues(alpha: 51) // 0.2 opacity
              : null,
      border: isSelected ? Border.all(color: AppColors.gfGreen) : null,
      child: Row(
        children: [
          // Channel avatar
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.gfGreen.withValues(alpha: 51), // 0.2 opacity
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                channel.name.isNotEmpty ? channel.name[0].toUpperCase() : '#',
                style: const TextStyle(
                  color: AppColors.gfGreen,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),

          // Channel info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  channel.name,
                  style: TextStyle(
                    color:
                        isSelected ? AppColors.gfGreen : AppColors.gfOffWhite,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${channel.memberCount} members',
                  style: const TextStyle(
                    color: AppColors.gfGrayText,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),

          // Trailing widget
          if (trailing != null) trailing!,
        ],
      ),
    );
  }
}

/// Channel list item with selection
class GFChannelListItem extends StatelessWidget {
  final ChannelModel channel;
  final bool isSelected;
  final VoidCallback? onTap;
  final bool showCheckbox;

  const GFChannelListItem({
    super.key,
    required this.channel,
    this.isSelected = false,
    this.onTap,
    this.showCheckbox = false,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppColors.gfGreen.withValues(alpha: 51), // 0.2 opacity
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            '#',
            style: const TextStyle(
              color: AppColors.gfGreen,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
      title: Text(
        channel.name,
        style: TextStyle(
          color: isSelected ? AppColors.gfGreen : AppColors.gfOffWhite,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      subtitle: Text(
        '${channel.memberCount} members',
        style: const TextStyle(color: AppColors.gfGrayText),
      ),
      trailing:
          showCheckbox
              ? Checkbox(
                value: isSelected,
                onChanged: onTap != null ? (_) => onTap!() : null,
                activeColor: AppColors.gfGreen,
              )
              : isSelected
              ? const Icon(Icons.check, color: AppColors.gfGreen)
              : null,
      onTap: onTap,
      tileColor:
          isSelected
              ? AppColors.gfGreen.withValues(alpha: 26) // 0.1 opacity
              : null,
    );
  }
}

/// Channel header for channel detail screens
class GFChannelHeader extends StatelessWidget {
  final ChannelModel channel;
  final bool isJoined;
  final VoidCallback? onJoin;
  final VoidCallback? onLeave;
  final VoidCallback? onEdit;
  final VoidCallback? onShare;

  const GFChannelHeader({
    super.key,
    required this.channel,
    this.isJoined = false,
    this.onJoin,
    this.onLeave,
    this.onEdit,
    this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: AppColors.gfDarkBackground,
        border: Border(bottom: BorderSide(color: AppColors.gfGrayBorder)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Channel info
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: AppColors.gfGreen.withValues(alpha: 51), // 0.2 opacity
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Center(
                  child: Text(
                    channel.name.isNotEmpty
                        ? channel.name[0].toUpperCase()
                        : '#',
                    style: const TextStyle(
                      color: AppColors.gfGreen,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      channel.name,
                      style: const TextStyle(
                        color: AppColors.gfOffWhite,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          '${channel.memberCount} members',
                          style: const TextStyle(
                            color: AppColors.gfGrayText,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Text(
                          '${channel.postCount} posts',
                          style: const TextStyle(
                            color: AppColors.gfGrayText,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Description
          if (channel.description != null &&
              channel.description!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              channel.description!,
              style: const TextStyle(
                color: AppColors.gfOffWhite,
                fontSize: 14,
                height: 1.4,
              ),
            ),
          ],

          // Action buttons
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child:
                    isJoined
                        ? GFButton(
                          text: 'Joined',
                          onPressed: onLeave,
                          type: GFButtonType.secondary,
                        )
                        : GFButton(
                          text: 'Join Channel',
                          onPressed: onJoin,
                          type: GFButtonType.primary,
                        ),
              ),
              const SizedBox(width: 12),
              GFButton(
                text: '',
                onPressed: onShare,
                type: GFButtonType.outline,
                icon: Icons.share,
                width: 48,
              ),
              if (onEdit != null) ...[
                const SizedBox(width: 8),
                GFButton(
                  text: '',
                  onPressed: onEdit,
                  type: GFButtonType.outline,
                  icon: Icons.edit,
                  width: 48,
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }
}
