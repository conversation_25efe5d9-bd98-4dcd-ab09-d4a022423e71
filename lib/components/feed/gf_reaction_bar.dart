import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

/// GameFlex reaction bar component
///
/// A Discord-style reaction display component that only shows reactions
/// that have been used and supports single reaction per user.
///
/// Example usage:
/// ```dart
/// GFReactionBar(
///   reactions: {'👍': 5, '❤️': 3, '😂': 1},
///   currentUserReaction: '👍',
///   onReact: (emoji) => _handleReaction(emoji),
///   onShowReactionPicker: () => _showEmojiPicker(),
/// )
/// ```
class GFReactionBar extends StatelessWidget {
  final Map<String, int> reactions;
  final String? currentUserReaction;
  final void Function(String emoji) onReact;
  final VoidCallback? onShowReactionPicker;
  final VoidCallback? onShowReactionDetails;

  const GFReactionBar({
    super.key,
    required this.reactions,
    this.currentUserReaction,
    required this.onReact,
    this.onShowReactionPicker,
    this.onShowReactionDetails,
  });

  @override
  Widget build(BuildContext context) {
    // Only show reactions that have been used (count > 0)
    final activeReactions =
        reactions.entries.where((entry) => entry.value > 0).toList();

    // Always show something - either reactions or just the add button
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        ...activeReactions.map(
          (entry) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: GFReactionChip(
              emoji: entry.key,
              count: entry.value,
              isSelected: currentUserReaction == entry.key,
              onTap: () => onReact(entry.key),
              onLongPress: onShowReactionDetails,
            ),
          ),
        ),
        if (onShowReactionPicker != null)
          GFAddReactionButton(
            onTap: onShowReactionPicker!,
            showLabel: activeReactions.isEmpty,
          ),
      ],
    );
  }
}

/// Individual reaction chip
class GFReactionChip extends StatelessWidget {
  final String emoji;
  final int count;
  final bool isSelected;
  final VoidCallback onTap;
  final VoidCallback? onLongPress;

  const GFReactionChip({
    super.key,
    required this.emoji,
    required this.count,
    required this.isSelected,
    required this.onTap,
    this.onLongPress,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      onLongPress: onLongPress,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? AppColors.gfGreen.withValues(alpha: 51) // 0.2 opacity
                  : AppColors.gfDarkBackground,
          border: Border.all(
            color: isSelected ? AppColors.gfGreen : AppColors.gfGrayBorder,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(emoji, style: const TextStyle(fontSize: 14)),
            const SizedBox(width: 4),
            Text(
              count.toString(),
              style: TextStyle(
                color: isSelected ? AppColors.gfGreen : AppColors.gfGrayText,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Add reaction button
class GFAddReactionButton extends StatelessWidget {
  final VoidCallback onTap;
  final bool showLabel;

  const GFAddReactionButton({
    super.key,
    required this.onTap,
    this.showLabel = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: AppColors.gfDarkBackground,
          border: Border.all(color: AppColors.gfGrayBorder, width: 1),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.add, color: AppColors.gfGrayText, size: 16),
            if (showLabel) ...[
              const SizedBox(width: 4),
              const Text(
                'React',
                style: TextStyle(
                  color: AppColors.gfGrayText,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Reaction summary for showing total reaction count
class GFReactionSummary extends StatelessWidget {
  final Map<String, int> reactions;
  final VoidCallback? onTap;

  const GFReactionSummary({super.key, required this.reactions, this.onTap});

  @override
  Widget build(BuildContext context) {
    final totalReactions = reactions.values.fold(
      0,
      (sum, count) => sum + count,
    );

    if (totalReactions == 0) {
      return const SizedBox.shrink();
    }

    final topEmojis =
        reactions.entries.where((entry) => entry.value > 0).toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: AppColors.gfDarkBackground,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Show top 3 emojis
            ...topEmojis
                .take(3)
                .map(
                  (entry) => Padding(
                    padding: const EdgeInsets.only(right: 2),
                    child: Text(
                      entry.key,
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ),
            const SizedBox(width: 4),
            Text(
              totalReactions.toString(),
              style: const TextStyle(
                color: AppColors.gfGrayText,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Reaction details modal content
class GFReactionDetails extends StatelessWidget {
  final Map<String, int> reactions;
  final Map<String, List<String>>? reactionUsers; // emoji -> list of usernames

  const GFReactionDetails({
    super.key,
    required this.reactions,
    this.reactionUsers,
  });

  @override
  Widget build(BuildContext context) {
    final activeReactions =
        reactions.entries.where((entry) => entry.value > 0).toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    return Container(
      constraints: const BoxConstraints(maxHeight: 400),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: AppColors.gfGrayBorder)),
            ),
            child: Row(
              children: [
                const Text(
                  'Reactions',
                  style: TextStyle(
                    color: AppColors.gfOffWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close, color: AppColors.gfGrayText),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          ),

          // Reaction list
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: activeReactions.length,
              itemBuilder: (context, index) {
                final reaction = activeReactions[index];
                final users = reactionUsers?[reaction.key] ?? [];

                return ListTile(
                  leading: Text(
                    reaction.key,
                    style: const TextStyle(fontSize: 24),
                  ),
                  title: Text(
                    '${reaction.value} ${reaction.value == 1 ? 'person' : 'people'}',
                    style: const TextStyle(
                      color: AppColors.gfOffWhite,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  subtitle:
                      users.isNotEmpty
                          ? Text(
                            users.take(3).join(', ') +
                                (users.length > 3
                                    ? ' and ${users.length - 3} more'
                                    : ''),
                            style: const TextStyle(color: AppColors.gfGrayText),
                          )
                          : null,
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
