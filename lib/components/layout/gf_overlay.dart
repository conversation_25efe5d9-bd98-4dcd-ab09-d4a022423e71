import 'package:flutter/material.dart';
import '../../theme/app_theme.dart';

/// GameFlex overlay components
///
/// A collection of overlay components for different UI scenarios.
/// Provides consistent overlay patterns across the app.

/// Base overlay component
class GFOverlay extends StatelessWidget {
  final Widget child;
  final Color? backgroundColor;
  final bool dismissible;
  final VoidCallback? onDismiss;
  final EdgeInsetsGeometry? padding;
  final AlignmentGeometry alignment;

  const GFOverlay({
    super.key,
    required this.child,
    this.backgroundColor,
    this.dismissible = true,
    this.onDismiss,
    this.padding,
    this.alignment = Alignment.center,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap:
          dismissible ? (onDismiss ?? () => Navigator.of(context).pop()) : null,
      child: Container(
        color:
            backgroundColor ??
            Colors.black.withValues(alpha: 128), // 0.5 opacity
        child: Align(
          alignment: alignment,
          child: GestureDetector(
            onTap: () {}, // Prevent dismissal when tapping content
            child: Padding(padding: padding ?? EdgeInsets.zero, child: child),
          ),
        ),
      ),
    );
  }
}

/// Top overlay for feed screens
class GFTopOverlay extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final bool showGradient;

  const GFTopOverlay({
    super.key,
    required this.child,
    this.padding,
    this.backgroundColor,
    this.showGradient = true,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          gradient:
              showGradient
                  ? LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Colors.transparent, Colors.transparent],
                  )
                  : null,
        ),
        child: Padding(
          // Reduced top padding since SafeArea is handled by parent
          // and we want content closer to the feed tabs
          padding: padding ?? const EdgeInsets.fromLTRB(16, 8, 16, 16),
          child: child,
        ),
      ),
    );
  }
}

/// Bottom overlay for feed screens
class GFBottomOverlay extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final bool showGradient;

  const GFBottomOverlay({
    super.key,
    required this.child,
    this.padding,
    this.backgroundColor,
    this.showGradient = true,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          gradient:
              showGradient
                  ? LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [Colors.transparent, Colors.transparent],
                  )
                  : null,
        ),
        child: SafeArea(
          top: false,
          child: Padding(
            padding: padding ?? const EdgeInsets.all(16),
            child: child,
          ),
        ),
      ),
    );
  }
}

/// Side overlay for drawer-like content
class GFSideOverlay extends StatelessWidget {
  final Widget child;
  final bool isLeft;
  final double width;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;

  const GFSideOverlay({
    super.key,
    required this.child,
    this.isLeft = true,
    this.width = 300,
    this.padding,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 0,
      bottom: 0,
      left: isLeft ? 0 : null,
      right: isLeft ? null : 0,
      width: width,
      child: Container(
        color: backgroundColor ?? AppColors.gfDarkBackground100,
        child: SafeArea(
          child: Padding(
            padding: padding ?? const EdgeInsets.all(16),
            child: child,
          ),
        ),
      ),
    );
  }
}

/// Modal overlay for dialogs and bottom sheets
class GFModalOverlay extends StatelessWidget {
  final Widget child;
  final bool isBottomSheet;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;

  const GFModalOverlay({
    super.key,
    required this.child,
    this.isBottomSheet = false,
    this.padding,
    this.backgroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    if (isBottomSheet) {
      return Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        child: Container(
          decoration: BoxDecoration(
            color: backgroundColor ?? AppColors.gfDarkBackground,
            borderRadius:
                borderRadius ??
                const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Padding(
            padding: padding ?? const EdgeInsets.all(16),
            child: child,
          ),
        ),
      );
    }

    return Center(
      child: Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: backgroundColor ?? AppColors.gfDarkBackground,
          borderRadius: borderRadius ?? BorderRadius.circular(12),
        ),
        child: Padding(
          padding: padding ?? const EdgeInsets.all(16),
          child: child,
        ),
      ),
    );
  }
}

/// Floating overlay for notifications or alerts
class GFFloatingOverlay extends StatelessWidget {
  final Widget child;
  final AlignmentGeometry alignment;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;
  final bool showShadow;

  const GFFloatingOverlay({
    super.key,
    required this.child,
    this.alignment = Alignment.topCenter,
    this.margin,
    this.backgroundColor,
    this.borderRadius,
    this.showShadow = true,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: Align(
        alignment: alignment,
        child: Container(
          margin: margin ?? const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: backgroundColor ?? AppColors.gfDarkBackground,
            borderRadius: borderRadius ?? BorderRadius.circular(8),
            boxShadow:
                showShadow
                    ? [
                      BoxShadow(
                        color: Colors.black.withValues(
                          alpha: 77,
                        ), // 0.3 opacity
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ]
                    : null,
          ),
          child: child,
        ),
      ),
    );
  }
}
